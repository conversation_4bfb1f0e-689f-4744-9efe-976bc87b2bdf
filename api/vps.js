import { HTTP } from '@/utils/http.js'

const HOST = 'https://unicity3dev-api.bdnrc.org.cn'
//const HOST = 'https://shuiyuzui.bdnrc.org.cn'
const baseUrl = '/xr/api/v1/ar'
//const AUTH_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIyOjExMjg0MDY4Mjg3NjM3NTQ0OTYiLCJyblN0ciI6ImpQT01tTFBLcmprdVZFcjE4NXNya3NGaWhYSFBESHJ2IiwiaWF0IjoxNzQ5NzIxMzQ0LCJpc3MiOiJCRE5SQy5PUkcuQ04iLCJqdGkiOiI4MjcxYzY5MDA0MTY0NDZjYjVkNWI2YmExYjkwZmY4MCIsInVzZXIiOiIxMTI4NDA2ODI4NzYzNzU0NDk2JDE3ODEzMjc4MzUwJDIiLCJ0eXBlIjoiVVNFUiJ9.N4rScgT_oBHTxvp6SgrmR7FHzunLsyE-P22N8WByVhQ'
const AUTH_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIyOjExMjg0MDY4Mjg3NjM3NTQ0OTYiLCJyblN0ciI6IlMyVGNaaTJRTHZ3bEVFUDBNTk9iMUdzc0pGamxTT25EIiwiaWF0IjoxNzU3NDA0MjkyLCJpc3MiOiJCRE5SQy5PUkcuQ04iLCJqdGkiOiI2NmQ4ZGFmMDQ2ODc0YWQ5YWU2YzNmYzE4NjNiNTQ3OSIsInVzZXIiOiIxMTI4NDA2ODI4NzYzNzU0NDk2JDE3ODEzMjc4MzUwJDIiLCJ0eXBlIjoiVVNFUiJ9.riOtTc4Lq6tI_IWk_ymb4r6m8hXt6LKwTxCTx9mEoBc'

class VpsApi extends HTTP {
  async vpsRoam(formData) {
    console.log('vpsRoam: '+JSON.stringify(formData.getData()))
	console.log('formData.getData().buffer is null: '+!formData.getData().buffer)
	return new Promise((resolve, reject) => {
		let data = formData.getData();
		wx.request({
		  url: `${HOST}${baseUrl}/roaming`,
		  method: 'POST',
		  data: data.buffer,
		  header: {
			'Content-Type': data.contentType,
			'Authorization': `Bearer ${AUTH_TOKEN}`
		  },
			success: (res) => {
			  if (res.data && res.data.code === 1) {
				console.log('vps tracking success: '+JSON.stringify(res.data))
				resolve(res.data.data); // 成功时返回助手回复
			  } else {
				console.log('vps tracking failed: '+JSON.stringify(res.data))
				reject(new Error('未能定位成功'));
			  }
			},
			fail: (error) => {
			  reject(error);
			}
		})
	})
   //  if (res && res.code === 1) {
   //    console.log('vps tracking success: '+JSON.stringify(res))
   //    return res.data; // 成功时返回助手回复
   //  } else {
	  // console.log('vps tracking failed: '+JSON.stringify(res))
   //    throw new Error('未能定位成功');
   //  }
  }
  
  getAssetList(projectId) {
	  return new Promise((resolve, reject) => {
	  		  wx.request({
	  		    url: `${HOST}${baseUrl}/project/asset/${projectId}`,
	  		    method: 'GET',
	  			header: {
	  				'Authorization': `Bearer ${AUTH_TOKEN}`
	  			},
	  			success: (res) => {
	  				if (res.data) {
	  					console.log('获取资产列表成功：'+JSON.stringify(res.data))
	  					resolve(res.data.data)
	  				} else {
	  					console.log('获取资产列表失败：'+JSON.stringify(res.data))
	  					reject(new Error('获取资产列表失败'))
	  				}
	  			},
	  			fail: (error) => {
	  				reject(error)
	  			}
	  		  })
	  })
  }
  
  downloadAnchorFile(data) {
	  return new Promise((resolve, reject) => {
		  wx.request({
		    url: `${HOST}${baseUrl}/market/asset/download`,
		    method: 'POST',
			data: data,
			header: {
				'Authorization': `Bearer ${AUTH_TOKEN}`
			},
			success: (res) => {
				if (res.data) {
					console.log('资源下载成功：'+JSON.stringify(res.data))
					resolve(res.data)
				} else {
					console.log('资源下载失败：'+JSON.stringify(res.data))
					reject(new Error('资源下载失败'))
				}
			},
			fail: (error) => {
				reject(error)
			}
		  })
	  })
  }
}

export { VpsApi }
