import { config, agentUrl } from '../config.js'

class HTTP {
	constructor() {
		this.baseUrl = config.base_url
	}

	request({
		url,
		data = {},
		method = 'GET',
		useOrigin = false,
		withoutAuth = false,
		isAgent = false,
		contentType = 'application/json'
	}) {
		return new Promise((resolve, reject) => {
			this._request(url, resolve, reject, data, method, useOrigin, withoutAuth, isAgent, contentType)
		})
	}

	streamRequest({
		url,
		data = {},
		method = 'GET',
		onChunkReceived,
		useOrigin = false,
		withoutAuth = false,
		contentType = 'application/json'
	}) {
		return new Promise((resolve, reject) => {
			this._streamRequest(
				url,
				resolve,
				reject,
				data,
				method,
				onChunkReceived,
				useOrigin,
				withoutAuth,
				contentType
			)
		})
	}

	_streamRequest(
		url,
		resolve,
		reject,
		data = {},
		method = 'GET',
		onChunkReceived,
		useOrigin = false,
		withoutAuth = false,
		contentType = 'application/json'
	) {
		console.log(useOrigin)
		const requestTask = uni.request({
			url: `${agentUrl}${url}`,
			method: method,
			data: data,
			header: {
				'content-type': contentType,
				appId: config.appId,
				...(!withoutAuth ? { Authorization: `Bearer ${uni.getStorageSync('AuthTokens')}` } : {})
			},
			responseType: 'text',
			enableChunked: true,
			success: (res) => {
				resolve(res)
			},
			fail: (err) => {
				reject()
				this._show_error(err.msg || err.message)
			}
		})

		requestTask.onChunkReceived((res) => {
			try {
				const uint8Array = new Uint8Array(res.data)
				const resText = decodeURIComponent(escape(String.fromCharCode.apply(null, uint8Array)))
				if (onChunkReceived) onChunkReceived(resText)
			} catch (error) {
				console.warn(error)
			}
		})

		return requestTask
	}

	_request(
		url,
		resolve,
		reject,
		data = {},
		method = 'GET',
		useOrigin = false,
		withoutAuth,
		isAgent = false,
		contentType = 'application/json'
	) {
		uni.request({
			url: isAgent ? `${agentUrl}${url}` : `${this.baseUrl}${url}`,
			method: method,
			data: data,
			header: {
				'content-type': contentType,
				appId: config.appId,
				...(!withoutAuth ? { Authorization: `Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIyOjkxNzg4ODg3Nzg4MDMyNDA5NiIsInJuU3RyIjoiWVBMY1lTRHR0bzBPeDRRZ1QzbWF0UVk3aExySjhaQ2siLCJpYXQiOjE3NDcwMjg1NTMsImlzcyI6IkJETlJDLk9SRy5DTiIsImp0aSI6ImJiMmQ3Zjg1MmIyMTQ5YTQ4YWNhNDk3ZTI2MzM1ZjRlIiwidXNlciI6IjkxNzg4ODg3Nzg4MDMyNDA5NiQxODgxMDk2MDY2NCQyIiwidHlwZSI6IlVTRVIifQ.qf9E6MqmExu0suhyGnVBOEdIbCOKB-kPA34nl1oulvA` } : {})
			},
			success: (res) => {
				if (res.data) {
					if (!useOrigin) {
						const _success =
							res.data.code === 1 || res.data.code === 200 || res.data.code === '200 OK'
						if (_success) {
							resolve(res.data)
						} else {
							if (isAgent) {
								reject(res.data.error)
								const error_code = res.data.status
								const _message = res.data.error
								this._show_error(error_code, _message)
							} else {
								reject(res.data.msg || res.data.message)
								const error_code = res.data.code
								const _message = res.data.msg || res.data.message
								this._show_error(error_code, _message)
							}
						}
					} else {
						resolve(res.data)
					}
				} else {
					reject(res.msg || res.message)
				}
			},
			fail: (err) => {
				reject()
				this._show_error(err.msg || err.message)
			}
		})
	}

	_show_error(error_code, _message) {
		uni.showToast({
			title: `${_message}`,
			icon: 'none',
			duration: 2000
		})
	}
}

export { HTTP }
