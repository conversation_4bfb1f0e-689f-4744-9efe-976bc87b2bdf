/**
 * 项目配置文件
 * 包含项目ID与GPS圆心坐标和半径的映射关系
 */

export const PROJECT_CONFIG = {
    // 项目5 - 数原测试（当前默认项目）
    '3': {
        name: '望仙阁',
        circleAreaCenter: {
            longitude: 116.33950124782987,
            latitude: 39.99294867621528
        },
        circleAreaRadius: 147.4, // 单位：米
    },
	'4': {
	    name: '免夫亭',
	    circleAreaCenter: {
	        longitude: 116.33950124782987,
	        latitude: 39.99294867621528
	    },
	    circleAreaRadius: 147.4, // 单位：米
	},
	'1': {
		name: '城门楼',
		circleAreaCenter: {
		    longitude: 116.04766967773438,
		    latitude: 39.967201199001735
		},
		circleAreaRadius: 28.546186777666307, // 20米即可
	},
	'2': {
		name: '铁匠铺',
		circleAreaCenter: {
		    longitude: 116.04525824652777,
		    latitude: 39.96716071234809
		},
		circleAreaRadius: 13.737391284712501, // 8米即可
	},
	'5': {
		name: '牛角岭关城',
		circleAreaCenter: {
		    longitude: 116.0430308285447,
		    latitude: 39.96507526803793
		},
		circleAreaRadius: 300000, // 15米即可 18.602777311092705
	},
	'7': {
		name: '秋思亭',
		circleAreaCenter: {
		    longitude: 116.04095650195964,
		    latitude: 39.963990942155675
		},
		circleAreaRadius: 35.60974114800153, // 30米即可
	},
	'1943': {
		name: '城门楼',
		circleAreaCenter: {
		    longitude: 116.04766967773438,
		    latitude: 39.967201199001735
		},
		circleAreaRadius: 28.546186777666307, // 20米即可
	},
	'1944': {
		name: '铁匠铺',
		circleAreaCenter: {
		    longitude: 116.04525824652777,
		    latitude: 39.96716071234809
		},
		circleAreaRadius: 13.737391284712501, // 8米即可
	},
	'1969': {
		name: '牛角岭关城',
		circleAreaCenter: {
		    longitude: 116.0430308285447,
		    latitude: 39.96507526803793
		},
		circleAreaRadius: 18.602777311092705, // 15米即可
	},
	'1941': {
		name: '秋思亭',
		circleAreaCenter: {
		    longitude: 116.04095650195964,
		    latitude: 39.963990942155675
		},
		circleAreaRadius: 300000, // 30米即可
	},
	'2139': {
		name: '工位',
		circleAreaCenter: {
		    longitude: 116.33950124782987,
		    latitude: 39.99294867621528
		},
		circleAreaRadius: 147.4, // 单位：米
	},
};

/**
 * 根据项目ID获取项目配置
 * @param {string} projectId - 项目ID
 * @returns {Object|null} 项目配置对象，如果不存在则返回null
 */
export function getProjectConfig(projectId) {
    return PROJECT_CONFIG[projectId] || null;
}

/**
 * 检查项目ID是否有效
 * @param {string} projectId - 项目ID
 * @returns {boolean} 是否有效
 */
export function isValidProjectId(projectId) {
    return PROJECT_CONFIG.hasOwnProperty(projectId);
}

/**
 * 获取所有可用的项目ID列表
 * @returns {Array<string>} 项目ID数组
 */
export function getAvailableProjectIds() {
    return Object.keys(PROJECT_CONFIG);
}

/**
 * 获取项目名称
 * @param {string} projectId - 项目ID
 * @returns {string} 项目名称，如果不存在则返回'未知项目'
 */
export function getProjectName(projectId) {
    const config = getProjectConfig(projectId);
    return config ? config.name : '未知项目';
}
