// 经纬度转换为米的常数因子（在北京附近40°N左右）
// 纬度1度约等于111km，经度1度约等于85km
const LAT_TO_METERS = 111000; // 纬度1度对应的米数
const LNG_TO_METERS = 85000;  // 经度1度对应的米数（在北京附近）

/**
 * 判断新的经纬度点是否在圆内，并计算到圆心的距离
 * @param {Array<number>} center - 圆心 [lng, lat]
 * @param {number} radiusInMeters - 圆半径（米）
 * @param {Array<number>} point - 要检查的点 [lng, lat]
 * @return {Object} 返回结果 {isInside: boolean, distanceInMeters: number}
 */
export function checkPointInCircle(center, radiusInMeters, point) {
  // 计算平面距离（近似）
  const latDiff = (point.latitude - center.latitude) * LAT_TO_METERS;
  const lngDiff = (point.longitude - center.longitude) * LNG_TO_METERS;

  const distanceInMeters = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);

  return {
    isInside: distanceInMeters <= radiusInMeters,
    distanceInMeters
  };
}