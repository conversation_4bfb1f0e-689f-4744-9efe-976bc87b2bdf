# AR性能监控功能

## 功能概述

本功能为AR应用提供实时的帧率和内存监控，当性能不佳时会自动提示用户，帮助提升AR体验的稳定性。

## 主要特性

### 1. 帧率监控
- 实时计算和显示当前FPS
- 计算平均FPS，避免瞬时波动影响判断
- 设置低帧率阈值（20 FPS）和严重低帧率阈值（15 FPS）
- 当帧率持续低于阈值时自动弹出警告

### 2. 内存监控
- 监听微信小程序的内存警告事件
- 记录内存警告次数
- 自动触发垃圾回收（如果可用）
- 防止频繁警告的冷却机制

### 3. 用户提示
- 智能弹窗提醒用户性能问题
- 针对不同严重程度提供不同的提示信息
- 建议用户重启微信来解决性能问题

## 使用方法

### 1. 在页面中集成

```javascript
// 导入性能监控工具
import performanceMonitor from '@/utils/performanceMonitor'

// 在页面加载时初始化
onLoad(() => {
  initPerformanceMonitor()
})

// 在页面卸载时清理
onUnload(() => {
  performanceMonitor.stopMonitoring()
})
```

### 2. 初始化监控

```javascript
const initPerformanceMonitor = () => {
  // 设置回调函数
  performanceMonitor.setCallbacks({
    onFPSUpdate: (fps) => {
      // 更新FPS显示
      currentFPS.value = fps
    },
    onMemoryWarning: (count) => {
      // 处理内存警告
      memoryWarningCount.value = count
    },
    onPerformanceAlert: (type, data) => {
      // 处理性能警告
      handlePerformanceAlert(type, data)
    }
  })
  
  // 开始监控
  performanceMonitor.startMonitoring()
}
```

### 3. 处理性能警告

```javascript
const handlePerformanceAlert = (type, data) => {
  let title = '性能提醒'
  let content = data.message
  
  switch (type) {
    case 'critical_fps':
      title = '严重性能警告'
      break
    case 'low_fps':
      title = '性能警告'
      break
    case 'memory_warning':
      title = '内存不足警告'
      break
  }
  
  wx.showModal({
    title,
    content,
    confirmText: '知道了',
    success: (res) => {
      if (res.confirm) {
        console.log('用户确认性能警告')
      }
    }
  })
}
```

## 界面显示

### 性能信息显示
在AR界面左上角显示实时性能信息：
- 当前FPS
- 平均FPS
- 内存警告次数

### 颜色编码
- 绿色：性能良好（FPS > 20）
- 橙色：性能警告（15 < FPS ≤ 20）
- 红色：性能严重（FPS ≤ 15 或有内存警告）

### 切换显示
提供"显示性能"/"隐藏性能"按钮，用户可以选择是否显示性能信息。

## 配置选项

可以通过 `updateConfig` 方法自定义监控参数：

```javascript
performanceMonitor.updateConfig({
  lowFPSThreshold: 25,        // 低帧率阈值
  criticalFPSThreshold: 18,   // 严重低帧率阈值
  fpsCheckInterval: 1500,     // FPS检查间隔(ms)
  memoryWarningCooldown: 15000, // 内存警告冷却时间(ms)
  maxFPSHistory: 15,          // 保存的FPS历史记录数量
})
```

## 兼容性

- 支持微信小程序环境
- 兼容不同的时间API（performance.now, wx.getPerformance, Date.now）
- 兼容不同的动画帧API（requestAnimationFrame, setTimeout）

## 注意事项

1. **性能影响**：监控本身会消耗少量性能，但影响微乎其微
2. **内存警告**：内存警告是系统级事件，无法完全避免
3. **帧率计算**：基于动画帧计算，可能与实际渲染帧率略有差异
4. **用户体验**：避免过于频繁的警告，设置了合理的冷却时间

## 故障排除

### 常见问题

1. **FPS显示为0**
   - 检查是否正确调用了 `startMonitoring()`
   - 确认页面没有被暂停或隐藏

2. **内存警告不触发**
   - 内存警告依赖系统，在内存充足时不会触发
   - 可以通过大量加载资源来测试

3. **性能警告过于频繁**
   - 调整 `fpsCheckInterval` 和阈值参数
   - 检查是否有其他性能问题

### 调试方法

```javascript
// 获取当前性能状态
const status = performanceMonitor.getPerformanceStatus()
console.log('性能状态:', status)

// 手动重置监控数据
performanceMonitor.reset()
```

## 更新日志

### v1.0.0
- 初始版本
- 支持帧率和内存监控
- 提供用户友好的警告提示
- 兼容微信小程序环境
