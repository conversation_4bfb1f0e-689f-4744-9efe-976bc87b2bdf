let BASE_URL = ''
let baseMapUrl = 'https://3dgs-unicity.bdnrc.org.cn?splatUrl='
// eslint-disable-next-line no-undef
BASE_URL = 'https://unicity3dev-api.bdnrc.org.cn' // qe环境,公司服务器
baseMapUrl = 'https://3dgs-unicity.bdnrc.org.cn?splatUrl='
// if (process.env.NODE_ENV == 'development') {
// 	BASE_URL = 'https://shuiyuzui-dev.bdnrc.org.cn' // qe环境,公司服务器
// 	baseMapUrl = 'https://3dgs-unicity.bdnrc.org.cn?splatUrl='
// } else {
// 	BASE_URL = 'https://shuiyuzui-dev.bdnrc.org.cn' // 生产环境
// 	baseMapUrl = 'https://3dgs-unicity.bdnrc.org.cn?splatUrl='
// }

const config = {
	base_url: BASE_URL,
	appId: 'wx6361d1155b715fbf',
	appKey: ''
}
const agentUrl = 'https://shuiyuzui-dev.bdnrc.org.cn/agent'
const imgBaseUrl = 'https://shuiyuzui-cdn.bdnrc.org.cn/static/'
const uploadImgPreviewUrl = 'https://shuiyuzuiimg.bdnrc.org.cn'

const uploadBasePath = '/oss/api/v1/oss/'

const AccessKey_ID = 'LTAI5tHit13nusTgqPS1KLcn'
const AccessKey_Secret = '******************************'
const speechUrl = 'wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1'
const AppKey = 'm0lbKolP2hgTecPS'

export {
	BASE_URL,
	config,
	imgBaseUrl,
	uploadBasePath,
	baseMapUrl,
	uploadImgPreviewUrl,
	AccessKey_ID,
	AccessKey_Secret,
	speechUrl,
	AppKey,
	agentUrl
}
