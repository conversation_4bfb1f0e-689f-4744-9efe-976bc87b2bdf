<template>
	<view class="content">
		<image class="logo" src="/static/logo.png"></image>
		<view class="text-area">
			<!-- <text class="title">{{title}}</text> -->
			<view class="input-container">
                <text class="input-label">项目ID：</text>
                <input
                    class="project-input"
                    type="text"
                    v-model="projectId"
                    placeholder="请输入项目ID"
                />
            </view>

            <!-- 显示当前项目信息（仅在配置加载后显示） -->
            <view class="project-info" v-if="currentProjectConfig">
                <text class="project-name">✓ {{ projectName }}</text>
            </view>

            <view class="input-container">
				<view>
					<text class="input-label">经度：</text>
					<input
					    class="location-input"
					    type="text"
					    v-model="longitude"
					    placeholder="经度"
					/>
				</view>
				<view>
					<text class="input-label">纬度：</text>
					<input
					    class="location-input"
					    type="text"
					    v-model="latitude"
					    placeholder="纬度"
					/>
				</view>
            </view>
			
			<view class="button-row">
				<button @click="getCurrentLocation" class="location-button">获取位置</button>
			</view>

			<view class="button-row">
                <button :disabled="isGettingLocation" :loading="isGettingLocation" @click="goToAR" class="ar-button">进入AR</button>
                <view class="ar-button">至圆心距离：{{ toCircleCenterDistance }}米</view>
                 <!-- <button class="ar-button" @click="stopLocationUpdate">停止位置更新</button> -->
            </view>
		</view>
	</view>
</template>

<script>
    import { checkPointInCircle } from '@/utils/calc.js';
    import { getProjectConfig, isValidProjectId } from '@/utils/projectConfig.js';
	export default {
		data() {
			return {
                title: 'Hello',
                projectId: '5', // 默认项目ID
                latitude: '', // 纬度
                longitude: '', // 经度
                isGettingLocation: false, // 是否正在获取位置
                toCircleCenterDistance: 0,
                currentProjectConfig: null // 当前项目配置
			}
		},
        computed: {
            // 获取当前项目的圆心坐标
            circleAreaCenter() {
                return this.currentProjectConfig ? this.currentProjectConfig.circleAreaCenter : null;
            },
            // 获取当前项目的半径
            circleAreaRadius() {
                return this.currentProjectConfig ? this.currentProjectConfig.circleAreaRadius : 0;
            },
            // 获取当前项目名称
            projectName() {
                return this.currentProjectConfig ? this.currentProjectConfig.name : '未知项目';
            }
        },

		onLoad() {
			this.getCurrentLocation();

            // wx.onLocationChange((res) => {
            //                 console.log('位置变化', res)
            //                 const latitude = res.latitude;
            //                 const longitude = res.longitude;

            //                         // 检查点是否在圆内
            //                 const { isInside, distanceInMeters } = checkPointInCircle(this.circleAreaCenter, this.circleAreaRadius, {
            //                     latitude: latitude,
            //                     longitude: longitude
            //                 });

            //                 console.log('圆心：', this.circleAreaCenter, '半径：', this.circleAreaRadius, '距离：', distanceInMeters, '是否在圆内：', isInside, '经纬度：', latitude, longitude);
            //             })
		},
		methods: {
            // 更新项目配置
            updateProjectConfig(projectId) {
                if (!projectId || projectId.trim() === '') {
                    this.currentProjectConfig = null;
                    return;
                }

                const config = getProjectConfig(projectId);
                if (config) {
                    this.currentProjectConfig = config;
                    console.log('项目配置已更新:', config);
                } else {
                    this.currentProjectConfig = null;
                    console.warn('未找到项目ID对应的配置:', projectId);
                }
            },

			// 获取当前位置
            getCurrentLocation() {
                if (this.isGettingLocation) return;

                this.isGettingLocation = true;
                uni.showLoading({
                    title: '获取位置中...'
                });

                uni.getLocation({
                    type: 'gcj02', // 使用国测局坐标系
                    success: (res) => {
                        console.log('获取位置成功:', res);
                        this.latitude = res.latitude;
                        this.longitude = res.longitude;
                    },
                    fail: (err) => {
                        console.error('获取位置失败:', err);
                        uni.showToast({
                            title: '获取位置失败，请手动输入或授权位置权限',
                            icon: 'none',
                            duration: 3000
                        });
                    },
                    complete: () => {
                        this.isGettingLocation = false;
                        uni.hideLoading();
                    }
                });
            },

            stopLocationUpdate() {
                wx.stopLocationUpdate();
            },
            // 导航到AR页面
            goToAR() {
                // 验证projectId是否有效
                if (!this.projectId || this.projectId.trim() === '') {
                    uni.showToast({
                        title: '请输入有效的项目ID',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                }

                // 验证项目ID是否存在于配置中
                if (!isValidProjectId(this.projectId)) {
                    uni.showToast({
                        title: `项目ID "${this.projectId}" 不存在，请检查输入`,
                        icon: 'none',
                        duration: 3000
                    });
                    return;
                }

                // 在点击进入AR时更新项目配置
                this.updateProjectConfig(this.projectId);

                // 确保项目配置已加载
                if (!this.currentProjectConfig) {
                    uni.showToast({
                        title: '项目配置加载失败，请重试',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                }

                // 验证经纬度是否有效

                if (this.isGettingLocation) return;

                this.isGettingLocation = true;
                // uni.showLoading({
                //     title: '获取位置中...'
                // });
	            console.log('获取定位权限', wx.getSystemSetting())
                        uni.authorize({
                            scope: 'scope.userLocation',
                            success: () => {
                                wx.startLocationUpdate({
                                type: 'gcj02',
                                success: (res) => {
                                    console.log('获取位置成功:', res);
                                }
                        })
                        wx.getLocation({
                            type: 'gcj02', // 使用国测局坐标系
                            isHighAccuracy: true,
                            highAccuracyExpireTime: 10000,
                            success: (res) => {
                                console.log('获取位置成功:', res);
                                const latitude = res.latitude;
                                const longitude = res.longitude;
								this.latitude = latitude;
								this.longitude = longitude;

                                // 检查点是否在圆内
                                const { isInside, distanceInMeters } = checkPointInCircle(this.circleAreaCenter, this.circleAreaRadius, {
                                    latitude: latitude,
                                    longitude: longitude
                                });

                                console.log('圆心：', this.circleAreaCenter, '半径：', this.circleAreaRadius, '距离：', distanceInMeters, '是否在圆内：', isInside, '经纬度：', latitude, longitude);

                                this.toCircleCenterDistance = distanceInMeters.toFixed(2);

                                if (!isInside) {
                                    uni.showToast({
                                        title: '当前位置不在指定区域内，请移动到指定区域内',
                                        icon: 'none',
                                        duration: 2000
                                    });
                                    return;
                                } else {
                                    // 导航到AR页面并传递参数
                                    uni.navigateTo({
                                        url: `/pages/AR/vps_raw?projectId=${this.projectId}&latitude=${latitude}&longitude=${longitude}`
                                    })
                                }
                            },
                            fail: (err) => {
                                console.error('获取位置失败:', err);
                                uni.showToast({
                                    title: '获取位置失败，请手动输入或授权位置权限',
                                    icon: 'none',
                                    duration: 3000
                                });
                            },
                            complete: () => {
                                this.isGettingLocation = false;
                                // uni.hideLoading();
                            }
                        });
                    },
                    fail: (err) => {
                        console.error('授权失败:', err);
                    }
                });
                // if (!this.isValidCoordinate(this.latitude, this.longitude)) {
                //     uni.showToast({
                //         title: '请输入有效的经纬度或获取当前位置',
                //         icon: 'none',
                //         duration: 2000
                //     });
                //     return;
                // }

                // //导航到AR页面并传递参数
                // uni.navigateTo({
                //     url: `/pages/AR/vps_raw?projectId=${this.projectId}&latitude=${this.latitude}&longitude=${this.longitude}`
                // })
            },

            // 验证经纬度是否有效
            isValidCoordinate(lat, lng) {
                if (!lat || !lng) return false;

                const latNum = parseFloat(lat);
                const lngNum = parseFloat(lng);

                if (isNaN(latNum) || isNaN(lngNum)) return false;

                // 纬度范围：-90到90
                if (latNum < -90 || latNum > 90) return false;

                // 经度范围：-180到180
                if (lngNum < -180 || lngNum > 180) return false;

                return true;
            }
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}

	.input-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        margin-bottom: 30rpx;
    }

    .project-info {
        width: 100%;
        padding: 20rpx;
        background-color: #f8f9fa;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        border-left: 4px solid #007AFF;
    }

    .project-name {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
    }

    .input-label {
        font-size: 32rpx;
        color: #333;
        margin-right: 20rpx;
    }

    .project-input, .location-input {
        flex: 1;
        height: 80rpx;
        border: 1px solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 32rpx;
    }

    .button-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        margin-top: 30rpx;
    }

    .location-button {
        width: 45%;
        height: 90rpx;
        line-height: 90rpx;
        background-color: #4CAF50;
        color: #fff;
        border-radius: 8rpx;
        font-size: 34rpx;
    }

    .ar-button {
        width: 100%;
        height: 90rpx;
        line-height: 90rpx;
        background-color: #007AFF;
        color: #fff;
        border-radius: 8rpx;
        font-size: 34rpx;
        margin-top: 20rpx;
    }
</style>
