<template>
	<view class="container">
		<xr-ar-plane-template 
			ref="arRef"
		>
		</xr-ar-plane-template>
		
<!-- 		<view class="xr-control">
		  <view v-if="arTracked" @click="requestVps">开始定位</view>
		</view> -->
	</view>
</template>

<script setup>
	import { reactive, computed, ref, onMounted } from 'vue'
	import { onLoad, onUnload } from '@dcloudio/uni-app'
	
	const arRef = ref(null);
	const arTracked = ref(true);
	
	const onArTracked = (tracked) => {
		arTracked.value = tracked;
	}
	
	const requestVps = async () => {
        if (!arRef.value) {
            console.log('arRef value is undefined')
            return
        }
        try {
            await arRef.value.requestVps();
        } catch (error) {
            console.error('requestVps error:', error);
        }
	}

	onMounted(() => {
		console.log('Component mounted, arRef:', arRef.value);
    });
	
</script>

<style scoped>
	.container {
	width: 100vw;
	height: 100vh;
	position: relative;
	}

	.xr-control {
	position: absolute;
	bottom: 100rpx;
	left: 100rpx;
	width: 100%;
	display: flex;
	justify-content: center;
	z-index: 100;
	}

	.xr-control view {
	background-color: rgba(0, 0, 0, 0.6);
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 40rpx;
	}

</style>
