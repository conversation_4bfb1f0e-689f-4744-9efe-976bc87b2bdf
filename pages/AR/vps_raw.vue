<template>
  <view class="container">
    <xr-ar-plane disable-scroll id="main-frame" ref="arRef" :width="frameParams.renderWidth"
      :height="frameParams.renderHeight" :style="frameStyle" :initialVpsTracked="initialVpsTracked"
      :transformMatrix="transformMatrix" :onArReady="handleArReady" :onArInit="handleArInit" :onArTracked="handleArTracked"
      :onArLost="handleArLost" :onCameraPoseTick="handleCameraPoseTick" :assetList="assetList" :onAssetListLoaded="handleAssetListLoaded"/>

    <text class="text-box-vps">{{ cameraPoseText }}</text>

    <!-- 性能监控显示 -->
    <view class="performance-monitor" v-if="showPerformanceInfo">
      <text class="performance-text" :class="performanceStatusClass">
        FPS: {{ currentFPS }} | 平均: {{ averageFPS }} | 内存警告: {{ memoryWarningCount }}
      </text>
    </view>

	<view class="top-right-button-container">
		<!-- 重定位按钮 -->
		<view v-if="initialVpsTracked" class="relocate-button" @click="requestVps">
		  <text>重定位</text>
		</view>
		<!-- 生成位姿按钮 -->
		<view class="generate-pose-button" @click="generatePose">
		  <text>生成位姿</text>
		</view>
		<!-- 性能监控切换按钮 -->
		<view class="performance-toggle-button" @click="togglePerformanceDisplay">
		  <text>{{ showPerformanceInfo ? '隐藏性能' : '显示性能' }}</text>
		</view>
	</view>

    <!-- 按钮容器 -->
    <view class="button-container">
      <!-- VPS定位按钮 -->
      <view v-if="!initialVpsTracked" class="vps-button" @click="startCountdown">
        <text>{{ countdownText }}</text>
      </view>
      <!-- 截图按钮 -->
      <view class="screenshot-button" @click="takeScreenshot">
        <text>截图</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, computed, ref, onMounted } from 'vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { calculateTransformationMatrix, composeRotationMappingMatrix, getTransformedPose, radianToAngle, matrixLeftToRightMatrixY, matrixToPose } from '@/wxcomponents/utils/transformUtils';
import { yuvToImage, saveBufferToFile, cropImageByRatio } from '@/wxcomponents/utils/yuvImageUtils'
import { AnchorData, AssetData } from './anchor-data';
import { FormData } from '@/utils/form-data/formData'
import { VpsApi } from '@/api/vps'
import performanceMonitor from '@/utils/performanceMonitor'

const xr = wx.getXrFrameSystem();

const vpsApi = new VpsApi()
const arRef = ref(null)
const lastVpsRoamingSuccess = ref(false)
const arTracked = ref(false)
const transformMatrix = ref(null)
const initialVpsTracked = ref(false)
const userPose = ref(null)
const arRawInfo = ref(null)
const anchorList = ref([])
const cameraPoseText = ref(null)
const assetList = ref([])
const assetListLoaded = ref(false);
const currentProjectId = ref('1950');
const currentLatitude = ref('39.992194'); // 默认纬度
const currentLongitude = ref('116.329943'); // 默认经度

// 性能监控相关变量
const showPerformanceInfo = ref(true) // 是否显示性能信息
const currentFPS = ref(0)
const averageFPS = ref(0)
const memoryWarningCount = ref(0)
const performanceStatus = ref('good') // good, warning, critical

const RequestStatus = {
  IDLE: 'idle',
  PENDING: 'pending',
  SUCCESS: 'success',
  FAIL: 'fail',
  ABORTED: 'aborted',
};

const requestVpsTask = ref(null);
const requestStatus = ref(RequestStatus.IDLE);

// 添加倒计时相关的状态
const countdownText = ref('开始体验')
const isCountingDown = ref(false)
const countdownInterval = ref(null)
const vpsRetryCount = ref(0)
const MAX_RETRY_COUNT = 2

const frameParams = reactive({
  width: 0,
  height: 0,
  renderWidth: 0,
  renderHeight: 0
})

const frameStyle = computed(() => ({
  width: frameParams.width + 'px',
  height: frameParams.height + 'px',
  top: 0,
  left: 0,
  display: 'block'
}))

// 性能状态样式类
const performanceStatusClass = computed(() => {
  switch (performanceStatus.value) {
    case 'critical':
      return 'performance-critical'
    case 'warning':
      return 'performance-warning'
    default:
      return 'performance-good'
  }
})

onLoad(async (options) => {
  const { windowWidth, windowHeight, pixelRatio } = uni.getWindowInfo()
  frameParams.width = windowWidth
  frameParams.height = windowHeight
  frameParams.renderWidth = windowWidth * pixelRatio
  frameParams.renderHeight = windowHeight * pixelRatio

  // 获取传递过来的参数
  if (options) {
    // 处理项目ID
    if (options.projectId) {
      console.log('接收到项目ID:', options.projectId);
      currentProjectId.value = options.projectId;
    } else {
      console.warn('未接收到项目ID，使用默认值');
    }

    // 处理经纬度
    if (options.latitude && options.longitude) {
      console.log('接收到经纬度:', options.latitude, options.longitude);
      currentLatitude.value = options.latitude;
      currentLongitude.value = options.longitude;
    } else {
      console.warn('未接收到经纬度，使用默认值');
    }
  }

  wx.showLoading({
	  title: '资产加载中'
  })

  // 可以在这里根据projectId加载相关资产
  await loadProjectAssets(currentProjectId.value);

  // 初始化性能监控
  initPerformanceMonitor();

  // const assetsData = await vpsApi.getAssetList('1950')
  // const assets = []
  // assetsData.forEach((asset) => {
	 //  assets.push(new AssetData(asset.assetId, 'gltf', asset.models[0]))
  // })
  // assetList.value = assets


  // if (!arTracked.value) {
  //   wx.showLoading({
  //     title: 'AR初始化',
  //   })
  // }
})

onUnload(() => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value)
    countdownInterval.value = null
  }
  if (requestVpsTask.value && requestStatus.value === RequestStatus.PENDING) {
	  requestVpsTask.value.abort();
  }

  // 停止性能监控
  performanceMonitor.stopMonitoring()
  console.log('性能监控已停止')
})

// 开始倒计时
const startCountdown = () => {
  if (isCountingDown.value || !arTracked.value) {
    return
  }

  isCountingDown.value = true
  let count = 5
  countdownText.value = `${count}`

  countdownInterval.value = setInterval(() => {
    count--
    if (count > 0) {
      countdownText.value = `${count}`
    } else {
      // 倒计时结束，执行VPS定位
      clearInterval(countdownInterval.value)
      countdownText.value = 'VPS定位中...'

      // 调用VPS定位函数
      requestVps().then(() => {
        // 定位完成后重置按钮文字
        setTimeout(() => {
          countdownText.value = 'VPS定位'
          isCountingDown.value = false
        }, 1000)
      }).catch(err => {
        console.error('VPS定位失败:', err)
        countdownText.value = 'VPS定位'
        isCountingDown.value = false
      })
    }
  }, 1000)
}

const generatePose = () => {
	if (!arTracked.value) {
	  return Promise.reject(new Error('AR未初始化'))
	}

	arRef.value.spawnCameraPoseMesh()
}

const tryGetFirstGlb = (modelUrls) => {
	if (!modelUrls || !Array.isArray(modelUrls) || modelUrls.length === 0) {
		console.log('modelUrls是空或未定义')
		return null;
	}
	const firstGlb = modelUrls.find(url => url.toLowerCase().endsWith('.glb') || url.toLowerCase().endsWith('.gltf'));
	return firstGlb;
}

// 添加这个函数来加载项目相关资产
const loadProjectAssets = async (projectId) => {
  try {
    console.log(`开始加载项目 ${projectId} 的资产`);

    const assetsData = await vpsApi.getAssetList(projectId);
    const assets = [];

    if (assetsData && Array.isArray(assetsData)) {
      assetsData.forEach((asset) => {
		const glbUrl = tryGetFirstGlb(asset.models);
		if (glbUrl) {
			assets.push(new AssetData(asset.assetId, 'gltf', glbUrl));
		}
      });
      assetList.value = assets;
      console.log(`成功加载项目 ${projectId} 的资产列表，共 ${assets.length} 个资产`);
    } else {
      console.warn(`项目 ${projectId} 没有可用资产或返回格式不正确`);
      assetList.value = [];
    }
  } catch (error) {
    console.error(`加载项目 ${projectId} 资产时出错:`, error);
    assetList.value = [];

    uni.showToast({
      title: '加载项目资产失败',
      icon: 'none',
      duration: 2000
    });
  }
};

const handleArReady = async (evt) => {
	wx.showLoading({
		title: 'AR初始化中'
	})
}

const handleAssetListLoaded = async (evt) => {
	assetListLoaded.value = true;
	wx.hideLoading()
}

// 初始AR初始化成功
const handleArInit = async (evt) => {
  arTracked.value = true
  wx.hideLoading()
}
const handleArLost = (evt) => {
  arTracked.value = false
  wx.showLoading({
    title: '追踪不稳定',
  })
}
// SLAM丢失后，AR再次初始化成功
const handleArTracked = async (evt) => {
  arTracked.value = true
  wx.hideLoading()
  if (initialVpsTracked.value) {
	  await requestVps();
  }
}

// 用于记录VPS纠偏后的相机位姿
const handleCameraPoseTick = (evt) => {
  //console.log('handleCameraPoseTick: '+JSON.stringify(evt.arRawData));
  const { cameraPos, cameraQuat, arRawData } = evt
  const cameraEuler = cameraQuat.toEulerAngles()

  cameraPoseText.value = "vpsCorrectedPose:x:" + cameraPos.x.toFixed(3) + ", y:" + cameraPos.y.toFixed(3) + ', z:' + cameraPos.z.toFixed(3) + ', 旋转:x:' + radianToAngle(cameraEuler.x).toFixed(3) + ', y:' + radianToAngle(cameraEuler.y).toFixed(3) + ', z:' + radianToAngle(cameraEuler.z).toFixed(3)

  if (!arRawInfo.value) {
    arRawInfo.value = arRawData
  }
  userPose.value = {
    position: { x: cameraPos.x, y: cameraPos.y, z: cameraPos.z },
    quaternion: cameraQuat
  }
}

// 申请初始定位
const requestVps = async () => {
  if (!arTracked.value) {
    return Promise.reject(new Error('AR未初始化'))
  }
  //此处使用原始相机的位姿，是因为原始相机位姿在经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
  let queryPose = {
    position: xr.Vector3.createFromNumber(userPose.value.position.x, userPose.value.position.y, userPose.value.position.z),
    quaternion: userPose.value.quaternion
  }

  requestVpsTask.value = await saveCurrentFrame(queryPose)
}

const saveCurrentFrame = async (queryPose) => {
  try {
    if (!arRawInfo.value) {
      console.log('arRawInfo is empty')
      return
    }
    wx.showLoading({
      title: '定位中',
    })
    const { yBuffer, uvBuffer, width, height, intrinsics } = arRawInfo.value
    const imageBuffer = await yuvToImage(yBuffer, uvBuffer, width, height)
    const vpsIntrinsics = [intrinsics[0], intrinsics[4], intrinsics[6], intrinsics[7], width, height]

    const formData = new FormData()
    formData.appendFile('file', imageBuffer, 'pic.jpg')
    formData.append('latitude', currentLatitude.value) // TODO: 替换真实GPS信息
    formData.append('longitude', currentLongitude.value) // TODO: 替换真实GPS信息
    formData.append('projectId', currentProjectId.value) // TODO: 替换真实项目ID
    formData.append('userId', "6") // TODO: 替换真实用户ID
    formData.append('type', 2)
    formData.append('intrinsics', vpsIntrinsics)

    const euler = queryPose.quaternion.toEulerAngles()

	requestStatus.value = RequestStatus.PENDING;
    const vpsInfo = await vpsApi.vpsRoam(formData)
	requestStatus.value = RequestStatus.SUCCESS;

    const vpsPosition = { x: vpsInfo.tcw[0], y: vpsInfo.tcw[1], z: vpsInfo.tcw[2] }
    const vpsQuaternion = xr.Quaternion.createFromNumber(vpsInfo.qcw[1], vpsInfo.qcw[2], vpsInfo.qcw[3], vpsInfo.qcw[0])
    const vpsEuler = vpsQuaternion.toEulerAngles()
    const vpsPose = {
      position: xr.Vector3.createFromNumber(vpsPosition.x, vpsPosition.y, vpsPosition.z),
      quaternion: vpsQuaternion
    }
    const cameraEuler = queryPose.quaternion.toEulerAngles()
    console.log('--------queryPose的值是------: position: x: ' + queryPose.position.x + ', y: ' + queryPose.position.y + ', z: ' + queryPose.position.z + ', rotation: x: ' + radianToAngle(cameraEuler.x) + ', y: ' + radianToAngle(cameraEuler.y) + ', z: ' + radianToAngle(cameraEuler.z))

    console.log(" --------vpsPose的值是------:" + JSON.stringify(vpsPose.position) + "," + "vpsPose rotation:" + JSON.stringify(vpsPose.quaternion.toEulerAngles()))

    transformMatrix.value = calculateTransformationMatrix(queryPose, vpsPose)
    vpsRetryCount.value = 0
    lastVpsRoamingSuccess.value = true

    wx.hideLoading()
    wx.showToast({
      title: '定位成功',
      icon: 'success'
    })
    const vpsAnchorList = vpsInfo.deltaPositionList
	//const audioUrl = vpsInfo.audioUrl || ''
    const anchors = []
    vpsAnchorList.forEach(anchor => {
      const anchorPositionArray = JSON.parse(anchor.position)
      const anchorRotationArray = JSON.parse(anchor.rotation)
      const anchorScaleArray = JSON.parse(anchor.scale)
      //从云测返回的anchor位姿
      const vpsAnchorPose = {
        position: xr.Vector3.createFromNumber(anchorPositionArray[0], anchorPositionArray[1], anchorPositionArray[2]),
        quaternion: xr.Quaternion.createFromNumber(anchorRotationArray[1], anchorRotationArray[2], anchorRotationArray[3], anchorRotationArray[0])
      }
      console.log("--------Anchor的VPSPose是------：" + JSON.stringify(vpsAnchorPose))
      const scale = { x: anchorScaleArray[0], y: anchorScaleArray[1], z: anchorScaleArray[2] }

      let vpsAnchorMatrix = xr.Matrix4.composeTQS(vpsAnchorPose.position, vpsAnchorPose.quaternion, xr.Vector3.ONE)

      let Tmatrix = new xr.Matrix4()
      Tmatrix.setArray(transformMatrix.value)
      console.log("--------Anchor的转换矩阵是------：" + JSON.stringify(matrixToPose(Tmatrix)))
      let slam_anchor_matrix = Tmatrix.multiply(vpsAnchorMatrix)

      let convertedMatrix = matrixLeftToRightMatrixY(slam_anchor_matrix)

      let convertedPose = matrixToPose(convertedMatrix)

      const convertedEuler = convertedPose.quaternion.toEulerAngles()
      const transformedPose2 = { position: { x: convertedPose.position.x, y: convertedPose.position.y, z: convertedPose.position.z }, rotation: { x: convertedEuler.x, y: convertedEuler.y, z: convertedEuler.z } }
      //console.log('transformedPose2: ' + JSON.stringify(transformedPose2))
	  const anchorUrl = tryGetFirstGlb(anchor.models)
	  if (anchorUrl) {
	    const anchorData = new AnchorData(anchor.anchorId, 'anchor-' + anchor.anchorId, transformedPose2.position, transformedPose2.rotation, scale, anchorUrl, anchor.assetId)
	    anchors.push(anchorData)
	  }
    });
    initialVpsTracked.value = true;
    anchorList.value = anchors;
    await refreshAnchorList()
	//playAudio(audioUrl)
  } catch (err) {
    console.log(JSON.stringify(err) + ', retry saveCurrentFrame')
    wx.hideLoading()
	requestStatus.value = RequestStatus.FAIL;

    // 检查重试次数
    if (vpsRetryCount.value < MAX_RETRY_COUNT) {
      vpsRetryCount.value++

      wx.showToast({
        title: `定位失败，第${vpsRetryCount.value}次重试`,
        icon: 'none',
        duration: 1000
      })

      lastVpsRoamingSuccess.value = false

      // 直接重试，不设置延迟
      await saveCurrentFrame(queryPose)
    } else {
      // 达到最大重试次数
      wx.showToast({
        title: '定位失败',
        icon: 'error',
        duration: 2000
      })

      // 重置计数器，为下一次手动尝试做准备
      vpsRetryCount.value = 0
      lastVpsRoamingSuccess.value = false

      // 更新按钮文字
      countdownText.value = 'VPS定位'
      isCountingDown.value = false
    }
  }
}

const refreshAnchorList = async () => {
  if (!arRef.value) {
    console.log('arRef is undefined')
    return
  }

  if (anchorList.value && anchorList.value.length > 0) {
	  if (!assetListLoaded.value) {
		wx.showLoading({title: '资产加载中'});
	  }
	  const anchorSpawnPromises = anchorList.value.map((anchor) => {
		  console.log('anchor' + anchor.id + ' position: ' + JSON.stringify(anchor.position) + ', rotation: x: ' + (anchor.rotation.x) + ', y: ' + (anchor.rotation.y) + ', z: ' + (anchor.rotation.z))
		  return arRef.value.spawnAnchorItem(anchor)
	  })
	  Promise.all(anchorSpawnPromises)
		.then(() => {
		  assetListLoaded.value = true;
		})
		.catch(err => {
		  wx.showToast({ title: '部分资产加载失败', icon: 'none' });
		  assetListLoaded.value = false;
		})
		.finally(() => {
		  wx.hideLoading();
		});
    // anchorList.value.forEach(async (anchor) => {
    //   console.log('anchor' + anchor.id + ' position: ' + JSON.stringify(anchor.position) + ', rotation: x: ' + (anchor.rotation.x) + ', y: ' + (anchor.rotation.y) + ', z: ' + (anchor.rotation.z))
    //   await arRef.value.spawnAnchorItem(anchor)
    // })
  } else {
    console.log('anchorList is empty')
  }
}

const takeScreenshot = async () => {
  if (!arRef.value) {
    console.log('arRef is undefined')
    return
  }

  const base64 = await arRef.value.takeScreenshot();
  const buffer = await cropImageByRatio(base64, 16/9, 'buffer')
  await saveBufferToFile(buffer);
}

// 性能监控相关函数
const initPerformanceMonitor = () => {
  // 设置性能监控回调
  performanceMonitor.setCallbacks({
    onFPSUpdate: (fps) => {
      currentFPS.value = fps
      const status = performanceMonitor.getPerformanceStatus()
      averageFPS.value = status.averageFPS
      performanceStatus.value = status.status
    },
    onMemoryWarning: (count) => {
      memoryWarningCount.value = count
    },
    onPerformanceAlert: (type, data) => {
      handlePerformanceAlert(type, data)
    }
  })

  // 开始监控
  performanceMonitor.startMonitoring()
  console.log('性能监控已初始化')
}

const handlePerformanceAlert = (type, data) => {
  let title = '性能提醒'
  let content = data.message
  let showCancel = true

  switch (type) {
    case 'critical_fps':
      title = '严重性能警告'
      showCancel = false
      break
    case 'low_fps':
      title = '性能警告'
      break
    case 'memory_warning':
      title = '内存不足警告'
      showCancel = false
      break
  }
  
  if (type === 'low_fps') {
	  wx.showToast({
		  title: content,
		  icon: 'none',
		  duration: 2000
	  })
	  return;
  }
  
  wx.showModal({
    title,
    content,
    showCancel,
    confirmText: '知道了',
    cancelText: '忽略',
    success: (res) => {
      if (res.confirm) {
        console.log('用户确认性能警告')
      }
    }
  })
}

</script>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.vps-button {
  background: linear-gradient(0deg, #ff9d30 1.14%, #ff9d30 52.14%, #ffb555 101.14%);
  color: white;
  padding: 12px 24px;
  border-radius: 44px;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  /* 确保按钮宽度足够显示倒计时文字 */
  transition: all 0.3s ease;
  /* 添加过渡效果 */
}

.vps-button text {
  font-size: 16px;
  text-align: center;
}

.top-right-button-container {
	position: absolute;
	top: 20px;
	right: 20px;
	transform: translateX(-50%);
	display: flex;
	flex-direction: column;
	gap: 20px;
	z-index: 100;
}

.relocate-button {
  background: linear-gradient(0deg, #3498db 1.14%, #3498db 52.14%, #5dade2 101.14%);
  color: white;
  padding: 10px 20px;
  border-radius: 44px;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.relocate-button text {
  font-size: 14px;
  text-align: center;
}

.generate-pose-button {
  background: linear-gradient(0deg, #3498db 1.14%, #3498db 52.14%, #5dade2 101.14%);
  color: white;
  padding: 10px 20px;
  border-radius: 44px;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.generate-pose-button text {
  font-size: 14px;
  text-align: center;
}

.performance-toggle-button {
  background: linear-gradient(0deg, #9C27B0 1.14%, #9C27B0 52.14%, #BA68C8 101.14%);
  color: white;
  padding: 8px 16px;
  border-radius: 44px;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.performance-toggle-button text {
  font-size: 12px;
  text-align: center;
}

.text-box-vps {
  position: absolute;
  top: 10vh;
  left: 30%;
  color: blue;
}

.screenshot-button {
  background: linear-gradient(0deg, #30a0ff 1.14%, #30a0ff 52.14%, #55b8ff 101.14%);
  color: white;
  padding: 12px 24px;
  border-radius: 44px;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-container {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  z-index: 100;
}

/* 性能监控样式 */
.performance-monitor {
  position: absolute;
  top: 60px;
  left: 20px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 12px;
  border-radius: 8px;
  z-index: 200;
}

.performance-text {
  font-size: 12px;
  font-weight: 500;
}

.performance-good {
  color: #4CAF50;
}

.performance-warning {
  color: #FF9800;
}

.performance-critical {
  color: #F44336;
}
</style>
