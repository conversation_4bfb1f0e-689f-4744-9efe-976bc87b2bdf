{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "uni-app"
			}
		},
		{
			"path" : "pages/AR/AR",
			"style" : 
			{
				"navigationBarTitleText" : "AR",
				"usingComponents": {
					"custom": "/wxcomponents/custom/index"
				}
			}
		},
		{
			"path" : "pages/AR/vps",
			"style" : 
			{
				"navigationBarTitleText" : "VPS",
				"usingComponents": {
					"xr-ar-plane-template": "/wxcomponents/xr-ar-plane-template/index"
				}
			}
		},
		{
			"path" : "pages/AR/vps_raw",
			"style" : 
			{
				"navigationBarTitleText" : "VPS_RAW",
				"pageOrientation": "landscape",
				"usingComponents": {
					"xr-ar-plane": "/wxcomponents/components/xr-ar-plane/index"
				}
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"pageOrientation": "landscape"
	},
	"uniIdRouter": {},
	"workers": "static/workers"
}
