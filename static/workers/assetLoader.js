// assetLoader.js - Web Worker 文件
let assetList = [];
let loadedCount = 0;
let pendingAssets = new Map(); // 跟踪正在加载的资产

// 处理主线程消息
onmessage = function(e) {
  const message = JSON.parse(e).data;
  console.log('onMessage: ' + JSON.stringify(message));
  
  if (message.type === 'loadAssets') {
    // 接收资产列表并开始加载
    assetList = message.assets;
    loadedCount = 0;
    pendingAssets.clear();
    
    if (assetList.length === 0) {
      console.log('没有资产需要加载');
      postMessage({
        type: 'complete',
        assets: assetList
      });
      return;
    }
    startLoadingBatch();
  } else if (message.type === 'assetLoaded') {
    const assetId = message.assetId;
    if (pendingAssets.has(assetId)) {
      pendingAssets.delete(assetId);
      if (message.success) {
        handleAssetLoaded(assetId);
      } else {
        handleAssetError(assetId, message.error);
      }
      if (pendingAssets.size === 0 && loadedCount < assetList.length) {
        startLoadingBatch();
      }
    }
  }
};

// 开始加载一批资产
function startLoadingBatch() {
  console.log('startLoadingBatch');
  const batchSize = 2;
  const startIndex = loadedCount;
  const endIndex = Math.min(startIndex + batchSize, assetList.length);

  for (let i = startIndex; i < endIndex; i++) {
    const asset = assetList[i];
    pendingAssets.set(asset.id, asset);
	console.log('postMessage: '+asset.id)
    postMessage({
      type: 'loadAsset',
      asset: asset
    });
  }
}

function handleAssetLoaded(assetId) {
  loadedCount++;
  console.log(`Worker: 资产加载成功: ${assetId}, 进度: ${loadedCount}/${assetList.length}`);
  postMessage({
    type: 'progress',
    loadedCount: loadedCount,
    total: assetList.length,
    progress: Math.floor((loadedCount / assetList.length) * 100)
  });
  if (loadedCount === assetList.length) {
    postMessage({
      type: 'complete',
      assets: assetList
    });
  }
}

function handleAssetError(assetId, error) {
  console.error(`Worker: 资产加载失败: ${assetId}`, error);
  postMessage({
    type: 'error',
    assetId: assetId,
    error: error
  });
  loadedCount++;
  if (loadedCount === assetList.length) {
    postMessage({
      type: 'complete',
      assets: assetList
    });
  }
}
