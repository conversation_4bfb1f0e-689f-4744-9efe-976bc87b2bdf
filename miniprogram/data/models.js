class AnchorPose {
  constructor(id, position, rotation) {
    this.id = id
    this.position = position
    this.rotation = rotation
  }
}

class AnchorData {
  constructor(id, name, position, rotation, scale, url, assetId) {
    this.id = id
    this.name = name
    this.position = {
      x: position.x,
      y: position.y,
      z: position.z
    }
    this.rotation = {
      x: rotation.x * 180 / Math.PI,
      y: rotation.y * 180 / Math.PI,
      z: rotation.z * 180 / Math.PI
    }
    this.scale = scale
    this.isActive = true
    this.assetId = assetId
    this.url = url
  }
}

module.exports = {
  AnchorPose,
  AnchorData
}