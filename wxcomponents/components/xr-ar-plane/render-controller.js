const xr = wx.getXrFrameSystem();

export class RenderController {
  constructor(cameraTrs, maxDistance = 2.0) {
    this.cameraTrs = cameraTrs;            // XRFrame 相机组件
    this.maxDistance = maxDistance;       // 渲染距离阈值
    this.nodes = new Set();               // 被管理的节点集合
    this.tempVec = new xr.Vector3();         // 临时变量用于避免频繁创建对象
  }

  /**
   * 注册一个节点加入距离裁剪
   * @param {XRNode} node - xr-node 节点对象
   */
  register(node) {
    if (node) {
      this.nodes.add(node);
    } else {
      console.warn('[RenderController] 无效节点注册失败', node);
    }
  }

  /**
   * 取消注册
   * @param {XRNode} node 
   */
  unregister(node) {
    this.nodes.delete(node);
  }

  /**
   * 每帧调用此方法以更新节点的可见性
   */
  update() {
    if (!this.cameraTrs) return;

    const camPos = this.cameraTrs.position;
    for (const node of this.nodes) {
		const nodeTrs = node.getComponent(xr.Transform)
		const nodePos = nodeTrs.position;
		const dist = nodePos.distanceTo(camPos);
		nodeTrs.visible = dist <= this.maxDistance;
		console.log(`node ${node.id}'s visibility:  `+nodeTrs.visible)
    }
  }

  /**
   * 清空所有注册节点
   */
  clear() {
    this.nodes.clear();
  }
}
