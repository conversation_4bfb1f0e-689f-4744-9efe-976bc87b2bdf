Component({
  properties: {
    // 可见距离（单位：米）
    renderDistance: {
      type: Number,
      value: 2.0
    },
    // 目标节点 ID 列表（字符串数组）
    targetNodeIds: {
      type: Array,
      value: []
    }
  },

  methods: {
    tick() {
      const scene = this.scene;
      const camera = scene.getCamera();
      const camPos = camera.getWorldPosition(new Vector3());

      const threshold = this.data.renderDistance;
      const ids = this.data.targetNodeIds;

      ids.forEach(id => {
        const node = scene.getElementById(id);
        if (!node) return;

        const nodePos = node.getWorldPosition(new Vector3());
        const dist = nodePos.distanceTo(camPos);

        const margin = 0.1; // 可调缓冲距离，防止闪烁
        node.visible = dist <= (threshold + margin);
      });
    },

    onStart() {
      console.log('RenderDistanceControl started');
    }
  }
});
