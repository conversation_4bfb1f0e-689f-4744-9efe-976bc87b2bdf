import sceneReadyBehavior from '../behavior-scene/scene-ready';
import { calculateTransformationMatrix, composeRotationMappingMatrix, getTransformedPose, radianToAngle, matrixLeftToRightMatrixY, matrixToPose } from './utils/transformUtils';
import { yuvToImage } from './utils/yuvImageUtils'
import AnchorData from './anchor-data';
import FormData from '../utils/form-data/formData'
import { VpsApi } from '@/api/vps'

let lastVpsRoamingSuccess = false
let vpsRetryTimeoutId = null

const xr = wx.getXrFrameSystem();

Page({
  behaviors: [sceneReadyBehavior],
  data: {
    anchorList: [],
    initialVpsTracked: false,
    showBackBtn: true,
    markerLeft: 50,
    markerTop: 50,
    markerWidth: 0,
    markerHeight: 0,
    checkDistanceIntervalId: null,
    transformMatrix: null,
    arTracked: false,
    // debug only
    cameraPoseText: ''
  },
  arRawData: null,
  userPose: null,

  markerLockComponent: null,
  onLoad() {
    if (!this.data.arTracked) {
      wx.showLoading({
        title: 'AR初始化',
      })
    }
  },
  onUnload() {
    if (vpsRetryTimeoutId) {
      clearTimeout(vpsRetryTimeoutId)
      vpsRetryTimeoutId = null
    }
  },
  // 申请初始定位
  async requestVps() {
    if (!this.data.arTracked) {
      return
    }
    // console.log('----请求定位时，相机的原始位姿是-----: x: '+this.origUserPose.position.x+', y: '+this.origUserPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)
    //此处使用原始相机的位姿，是因为原始相机位姿在经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
    let queryPose = {
      position: xr.Vector3.createFromNumber(this.userPose.position.x, this.userPose.position.y, this.userPose.position.z),
      quaternion: this.userPose.quaternion
    }

    await this.saveCurrentFrame(queryPose)
  },
  async saveCurrentFrame(queryPose) {
    try {
      if (!this.arRawData) {
        console.log('arRawData is empty')
        return
      }
      wx.showLoading({
        title: '定位中',
      })
      const {yBuffer, uvBuffer, width, height, intrinsics} = this.arRawData
      const imageBuffer = await yuvToImage(yBuffer, uvBuffer, width, height)
      const vpsIntrinsics = [intrinsics[0],intrinsics[4],intrinsics[6],intrinsics[7],width,height]
      console.log('intrinsics raw: '+intrinsics)
      console.log('intrinsics: '+vpsIntrinsics)
      const formData = new FormData()
      formData.appendFile('file', imageBuffer, 'pic.jpg')
      formData.append('latitude', 39.992194)
      formData.append('longitude', 116.329943)
      formData.append('type', 2)
      formData.append('intrinsics', vpsIntrinsics)

      const euler = queryPose.quaternion.toEulerAngles()
      console.log('queryPose: position: x: '+queryPose.position.x+', y: '+queryPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)

      const vpsInfo = await VpsApi.vpsRoam(formData)
      const vpsPosition = {x: vpsInfo.tcw[0], y: vpsInfo.tcw[1], z: vpsInfo.tcw[2]}
      const vpsQuaternion = xr.Quaternion.createFromNumber(vpsInfo.qcw[1], vpsInfo.qcw[2], vpsInfo.qcw[3], vpsInfo.qcw[0])
      const vpsEuler = vpsQuaternion.toEulerAngles()
      const vpsPose = { 
        position: xr.Vector3.createFromNumber(vpsPosition.x, vpsPosition.y, vpsPosition.z),
        quaternion: vpsQuaternion
      }
      const cameraEuler = queryPose.quaternion.toEulerAngles()
      console.log('--------queryPose的值是------: position: x: '+queryPose.position.x+', y: '+queryPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+radianToAngle(cameraEuler.x)+', y: '+radianToAngle(cameraEuler.y)+', z: '+radianToAngle(cameraEuler.z))

      console.log(" --------vpsPose的值是------:"+JSON.stringify(vpsPose.position) +","+"vpsPose rotation:"+ JSON.stringify(vpsPose.quaternion.toEulerAngles()))

      const transformMatrix = calculateTransformationMatrix(queryPose, vpsPose)
      this.setData({
        transformMatrix: transformMatrix
      })
      lastVpsRoamingSuccess = true
      if (vpsRetryTimeoutId) {
        clearTimeout(vpsRetryTimeoutId)
        vpsRetryTimeoutId = null
      }
      wx.hideLoading()
      wx.showToast({
        title: '定位成功',
        icon: 'success'
      })
        const vpsAnchorList = vpsInfo.deltaPositionList
        const anchorList = []
        vpsAnchorList.forEach(anchor => {
          const anchorPositionArray = JSON.parse(anchor.position)
          const anchorRotationArray = JSON.parse(anchor.rotation)
          const anchorScaleArray = JSON.parse(anchor.scale)
          //从云测返回的anchor位姿
          const vpsAnchorPose = {
            position: xr.Vector3.createFromNumber(anchorPositionArray[0], anchorPositionArray[1], anchorPositionArray[2]),
            quaternion: xr.Quaternion.createFromNumber(anchorRotationArray[1],anchorRotationArray[2],anchorRotationArray[3],anchorRotationArray[0])
          }
          console.log("--------Anchor的VPSPose是------："+ JSON.stringify(vpsAnchorPose))
          const scale = {x: anchorScaleArray[0], y: anchorScaleArray[1], z: anchorScaleArray[2]}

          let vpsAnchorMatrix = xr.Matrix4.composeTQS(vpsAnchorPose.position, vpsAnchorPose.quaternion, xr.Vector3.ONE)

          let Tmatrix = new xr.Matrix4()
           Tmatrix.setArray(transformMatrix)
           console.log("--------Anchor的转换矩阵是------："+ JSON.stringify(matrixToPose(Tmatrix)))
          let slam_anchor_matrix = Tmatrix.multiply(vpsAnchorMatrix)

          let convertedMatrix = matrixLeftToRightMatrixY(slam_anchor_matrix)

          let convertedPose = matrixToPose(convertedMatrix)

          const convertedEuler = convertedPose.quaternion.toEulerAngles()
          const transformedPose2 = {position: {x: convertedPose.position.x, y: convertedPose.position.y, z: convertedPose.position.z}, rotation: {x: convertedEuler.x, y: convertedEuler.y, z: convertedEuler.z}}
          //console.log('transformedPose2: '+JSON.stringify(transformedPose2))
          const anchorData = new AnchorData(anchor.anchorId, 'anchor-'+anchor.anchorId, transformedPose2.position, transformedPose2.rotation, scale)
          anchorList.push(anchorData)
        });
        this.setData({
          anchorList: anchorList,
          initialVpsTracked: true
        })
        this.refreshAnchorList()
    } catch(err) {
      console.log(err+', retry saveCurrentFrame')
      wx.hideLoading()
      wx.showToast({
        title: '定位失败',
        icon: 'error'
      })
      lastVpsRoamingSuccess = false
      // 确保仅设置一个重试计时器
      // if (!vpsRetryTimeoutId) {
      //   vpsRetryTimeoutId = setTimeout(async () => {
      //     vpsRetryTimeoutId = null;  // 重置定时器标志
      //     await this.saveCurrentFrame(queryPose)
      //   }, 5000)
      // }
    }
  },
  spawnCameraMesh() {
    if (!this.markerLockComponent) {
      this.markerLockComponent = this.selectComponent('#main-frame')
    }
    if (this.markerLockComponent) {
      this.markerLockComponent.spawnCameraPoseMesh()
    }
  },
  refreshAnchorList() {
    if (!this.markerLockComponent) {
      this.markerLockComponent = this.selectComponent('#main-frame')
    }
    if (this.markerLockComponent) {
      this.data.anchorList.forEach(anchor => {
        console.log('anchor' + anchor.id + ' position: '+JSON.stringify(anchor.position)+', rotation: x: '+(anchor.rotation.x)+', y: '+(anchor.rotation.y)+', z: '+(anchor.rotation.z))
        this.markerLockComponent.spawnAnchorItem(anchor)
      })
      //this.markerLockComponent.refreshActiveAnchorList(this.data.anchorList)
    } else {
      console.log('Cannot find markerLock component in the page')
    }
  },
  // 初始AR初始化成功
  handleArInit(evt) {
    this.setData({
      arTracked: true
    })
    wx.hideLoading()
  },
  handleArLost(evt) {
    this.setData({
      arTracked: false
    })
    wx.showLoading({
      title: 'AR初始化',
    })
  },
  // SLAM丢失后，AR再次初始化成功
  handleArTracked(evt) {
    this.setData({
      arTracked: true
    })
    wx.hideLoading()
  },
  // 用于记录VPS纠偏后的相机位姿
  handleCameraPoseTick(evt) {
    const {cameraPos, cameraQuat, arRawData} = evt.detail
    const cameraEuler = cameraQuat.toEulerAngles()
    this.setData({
      cameraPoseText: "vpsCorrectedPose:x:"+cameraPos.x.toFixed(3)+", y:"+cameraPos.y.toFixed(3)+', z:'+cameraPos.z.toFixed(3)+', 旋转:x:'+radianToAngle(cameraEuler.x).toFixed(3)+', y:'+radianToAngle(cameraEuler.y).toFixed(3)+', z:'+radianToAngle(cameraEuler.z).toFixed(3),
    })
    if (!this.arRawData) {
      this.arRawData = arRawData
    }
    this.userPose = {
      position: {x: cameraPos.x, y: cameraPos.y, z: cameraPos.z},
      quaternion: cameraQuat
    }

    // 用于实时检测anchor与摄像机之间的距离
    // if (!this.data.isProximityCheckOn) {
    //   this.resumeTracking()
    // }
  }
});