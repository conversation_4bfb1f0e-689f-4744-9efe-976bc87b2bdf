<view>
  <xr-ar-plane
    disable-scroll
    id="main-frame"
    width="{{renderWidth}}"
    height="{{renderHeight}}"
    style="width:{{width}}px;height:{{height}}px;top:{{top}}px;left:{{left}}px;display:block;"
    initialVpsTracked="{{initialVpsTracked}}"
    transformMatrix="{{transformMatrix}}"
    bindarInit="handleArInit"
    bindarTracked="handleArTracked"
    bindarLost="handleArLost"
    bindcameraPoseTick="handleCameraPoseTick"
  />

  <text class="text-box-vps">{{cameraPoseText}}</text>

  <view class="xr-control" style="top: {{height}}px;">
    <button bindtap="requestVps" wx:if="{{arTracked}}">开始定位</button>
    <!-- <button bindtap="spawnCameraMesh" wx:if="{{arTracked}}">生成相机Pose</button> -->
  </view>
</view>